#!/usr/bin/env python3

import sys
import os
import numpy as np
import time

# Add current directory to path
sys.path.insert(0, '.')

def test_full_execution():
    """Test the complete task execution."""
    print("Testing full task execution...")
    
    try:
        # Import modules
        import ravens.tasks
        from ravens.environments.environment import Environment
        import pybullet as p
        print("✓ Modules imported")
        
        # Create task
        task_class = ravens.tasks.names['gripper-pick-red-cube']
        task = task_class()
        print("✓ Task created")
        
        # Create environment
        print("Creating environment...")
        env = Environment(
            assets_root='./ravens/environments/assets/',
            disp=False,  # No GUI
            shared_memory=False,
            hz=480
        )
        print("✓ Environment created")
        
        # Set task and reset
        env.set_task(task)
        obs, info = env.reset()
        print("✓ Environment reset")
        
        # Check initial state
        if task.cube_id is None:
            print("✗ Cube not created")
            return False
        
        initial_cube_pose = p.getBasePositionAndOrientation(task.cube_id)
        initial_height = initial_cube_pose[0][2]
        print(f"✓ Initial cube height: {initial_height:.4f}")
        
        # Get oracle action
        oracle = task.oracle(env)
        action = oracle.act(obs, info)
        
        if not action:
            print("✗ Oracle returned None")
            return False
        
        print(f"✓ Oracle action: pick={action['pose0'][0]}, place={action['pose1'][0]}")
        
        # Execute action
        print("Executing action...")
        obs, reward, done, info = env.step(action)
        print(f"✓ Action executed")
        print(f"  - Reward: {reward}")
        print(f"  - Done: {done}")
        print(f"  - Step info: {info}")
        
        # Check final state
        final_cube_pose = p.getBasePositionAndOrientation(task.cube_id)
        final_height = final_cube_pose[0][2]
        height_increase = final_height - initial_height
        
        print(f"✓ Final cube height: {final_height:.4f}")
        print(f"✓ Height increase: {height_increase:.4f}")
        print(f"✓ Target height increase: {task.lift_height}")
        
        # Check task reward
        task_reward, task_info = task.reward()
        print(f"✓ Task reward: {task_reward}")
        print(f"  - Task info: {task_info}")
        
        # Check if task is done
        task_done = task.done()
        print(f"✓ Task done: {task_done}")
        
        # Success criteria
        success = (
            height_increase >= (task.lift_height - 0.05) and  # Within 5cm of target
            task_info.get('lifted', False) and
            task_info.get('stable', False)
        )
        
        if success:
            print("🎉 Task execution successful!")
        else:
            print("⚠️  Task execution completed but not successful")
            print(f"   - Height increase: {height_increase:.4f} (target: {task.lift_height})")
            print(f"   - Lifted: {task_info.get('lifted', False)}")
            print(f"   - Stable: {task_info.get('stable', False)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_full_execution()
    
    if success:
        print("\n🎉 Full execution test completed!")
    else:
        print("\n❌ Full execution test failed!")
    
    sys.exit(0 if success else 1)
