#!/usr/bin/python
# coding=utf-8
# Copyright 2021 The Ravens Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""<PERSON><PERSON><PERSON> pick red block task."""

import numpy as np
import pybullet as p
import os
import collections # Add collections import

from ravens.tasks.task import Task
from ravens.utils import utils
from ravens.tasks.grippers import RobotiqGripper
from ravens.tasks.primitives import PickPlaceGripper # Import the new primitive


class GripperPickRedBlockTask(Task):
  """Gripper pick red block task."""

  task_completed_desc = "successfully picked and lifted the red block."
  max_height_increase = 0.15 # Max height increase for place pose
  grasp_z_offset = 0.0 # Offset in z for grasping point relative to object center. For side grasp, this is 0.
  lift_height = 0.15 # Target lift height for reward and place pose
  block_size = np.array([0.04, 0.04, 0.04]) # Keep this consistent, make it an np.array

  def __init__(self, *args, **kwargs):
    super().__init__(*args, **kwargs)
    self.max_steps = 3 # Oracle should solve it in one pick-place action
    self.ee = RobotiqGripper
    # Use a slightly larger height for pre-moves in PickPlaceGripper than final lift_height
    self.primitive = PickPlaceGripper(height=self.lift_height + 0.05, speed=0.005)
    self.object_ids = [] # Will store the block ID
    self.initial_block_pose = None # Store initial pose (pos, orn) of the block

  def reset(self, env):
    obs_info_tuple = super().reset(env) # Capture return from base reset
    self.object_ids = []
    self.initial_block_pose = None

    # Add red block.
    block_urdf = 'box/box-template.urdf'
    
    # Get random pose, ensuring it's on the table (e.g., z=block_size[2]/2)
    # self.get_random_pose returns (pos, orn)
    random_pose = self.get_random_pose(env, self.block_size)
    if random_pose is None or random_pose[0] is None:
        # Fallback if no free space found, though unlikely in an empty scene
        block_pos = (self.workspace[0,0] + self.workspace[0,1])/2, \
                    (self.workspace[1,0] + self.workspace[1,1])/2, \
                    self.block_size[2] / 2.0
        block_orn_e = (0,0, np.random.uniform(-np.pi, np.pi))
        block_orn = utils.eulerXYZ_to_quatXYZW(block_orn_e)
        block_pose = (block_pos, block_orn)
    else:
        block_pose = random_pose

    # Ensure block is flat on the table for simplicity in calculating grasp poses
    # Overwrite random rotation around x, y axes if get_random_pose provides them.
    current_pos, current_orn_q = block_pose
    _, _, z_rot = utils.quatXYZW_to_eulerXYZ(current_orn_q)
    flat_orn_q = utils.eulerXYZ_to_quatXYZW((0, 0, z_rot))
    block_pose = (current_pos, flat_orn_q)
    
    # Call env.add_object without fixed_base or color arguments
    block_id = env.add_object(block_urdf, block_pose) 
    p.changeVisualShape(block_id, -1, rgbaColor=[1, 0, 0, 1]) # Set color to red
    p.changeDynamics(block_id, -1, mass=0.05) # Ensure block has mass and is not static
    self.object_ids.append(block_id)
    self.initial_block_pose = block_pose # Store (pos, orn) tuple

    # Define goals - The goal is to lift the block.
    # The target pose for the block is its initial XY, initial Z + lift_height, and initial orientation.
    target_pos = np.array(self.initial_block_pose[0]) + np.array([0, 0, self.lift_height])
    target_orn_quat = self.initial_block_pose[1] 

    # Ensure target_pos is a tuple for the goal definition
    self.goals.append(([block_id], np.eye(1), [(tuple(target_pos), target_orn_quat)],
                       False, True, 'pose', None, 1.0))
    
    # The task-specific reset is done. Environment.reset() will handle getting and returning obs, info.
    # No explicit return needed from here.

  def oracle(self, env, **kwargs):
    """Oracle agent for picking and lifting the red block with a gripper."""
    OracleAgentNamed = collections.namedtuple('OracleAgent', ['act'])

    def act_fn(obs, info): # Renamed to avoid conflict if base class is called
      del obs, info # obs and info are not used by this specific oracle logic
      if not self.object_ids or self.initial_block_pose is None:
        return None 
      
      block_id = self.object_ids[0]
      obj_initial_pos, obj_initial_orn_q = self.initial_block_pose
      current_obj_pose = p.getBasePositionAndOrientation(block_id)
      current_obj_pos, current_obj_orn_q = current_obj_pose

      grasp_pos_ee = list(current_obj_pos)
      obj_yaw = utils.quatXYZW_to_eulerXYZ(current_obj_orn_q)[2]
      grasp_yaw_ee = obj_yaw 
      grasp_orn_ee_q = utils.eulerXYZ_to_quatXYZW((0, 0, grasp_yaw_ee))
      pose0 = (tuple(grasp_pos_ee), grasp_orn_ee_q)

      target_block_pos_world = np.array(obj_initial_pos) + np.array([0, 0, self.lift_height])
      # target_block_orn_world_q = obj_initial_orn_q # Object's target orientation

      ee_displacement_world = target_block_pos_world - np.array(current_obj_pos)
      place_pos_ee = np.array(grasp_pos_ee) + ee_displacement_world
      place_orn_ee_q = grasp_orn_ee_q 
      pose1 = (tuple(place_pos_ee), place_orn_ee_q)
      
      # The action for a primitive-based oracle is typically the parameters for the primitive.
      # The primitive then gets called by the environment's step function using these params.
      # The base OracleAgent's act usually returns a dict like {'pose0': pick_pose, 'pose1': place_pose}
      # which is then passed to the primitive by the environment usually.
      # Or, if the agent itself is the primitive, its act could directly execute.
      # In demos.py: env.step(act) is called.
      # Environment.step(action) calls self.agent.primitive(self.movej, self.movep, self.ee, pose0, pose1)
      # So, the 'act' returned by agent.act() must be the dict {'pose0':..., 'pose1':...}

      return {'pose0': pose0, 'pose1': pose1}

    # Construct and return the namedtuple, similar to base Task._discrete_oracle
    return OracleAgentNamed(act=act_fn)

  def reward(self):
    """Return delta reward for the task; 1.0 for successful lift, 0.0 otherwise."""
    if not self.object_ids or self.initial_block_pose is None:
      return 0.0, {}
    
    block_id = self.object_ids[0]
    current_block_pose_world = p.getBasePositionAndOrientation(block_id)
    current_block_z = current_block_pose_world[0][2]
    
    step_reward = 0.0
    info = {'lifted': False, 'task_completed': False}

    # Target Z for the block based on its initial position and desired lift height.
    initial_block_z = self.initial_block_pose[0][2]
    target_final_block_z = initial_block_z + self.lift_height

    # Condition for successful lift.
    if current_block_z >= (target_final_block_z - 0.025): # Lift height nearly achieved
        # Check horizontal stability: ensure block is near its target XY.
        # The target XY for the block is its initial XY.
        target_block_xy_world = self.initial_block_pose[0][:2]
        current_block_xy_world = current_block_pose_world[0][:2]
        
        xy_dist_sq = (current_block_xy_world[0] - target_block_xy_world[0])**2 + \
                     (current_block_xy_world[1] - target_block_xy_world[1])**2
        
        # Max 3cm deviation in XY from where it should be after lifting straight up.
        if xy_dist_sq < (0.03**2): 
            step_reward = 1.0
            info['lifted'] = True

    # Calculate delta reward and update cumulative rewards and goals.
    # This logic is from the base Task.reward() method.
    if not self.goals: # Should not happen if step_reward is < 1.0, but good check.
        delta_r = 0.0
    else:
        # Calculate what the new cumulative reward would be with this step_reward.
        current_cumulative_reward = self.progress + step_reward
        delta_r = current_cumulative_reward - self._rewards
        self._rewards = current_cumulative_reward

        # Check if the current goal is met.
        # self.goals[0][7] is the max_reward for the current goal (which is 1.0 for us).
        if step_reward >= self.goals[0][7] - 0.01: # If step_reward is essentially max_reward for goal
            self.progress += self.goals[0][7]
            self.goals.pop(0)
            info['task_completed'] = True # Mark that the main goal of this task is done.
            # After goal is popped, self.done() will become true.

    return delta_r, info

  def done(self):
    """Task is done if all goals are achieved (i.e. block lifted successfully)."""
    # If self.goals is empty, it means the reward function determined the goal was met and popped it.
    # self._rewards > 0.99 is a fallback, common in other tasks.
    return len(self.goals) == 0 or self._rewards > 0.99

  # TODO: Implement oracle if necessary, or rely on primitive's oracle. 