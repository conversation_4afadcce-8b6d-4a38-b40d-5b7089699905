#!/usr/bin/python
# coding=utf-8
# Copyright 2021 The Ravens Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""<PERSON><PERSON><PERSON> pick red cube task - A simple task for gripper-based manipulation."""

import numpy as np
import pybullet as p
import collections

from ravens.tasks.task import Task
from ravens.utils import utils
from ravens.tasks.grippers import RobotiqGripper
from ravens.tasks.primitives import PickPlaceGripper


class GripperPickRedCube(Task):
  """Simple gripper task: pick up a red cube and lift it to a target height."""

  def __init__(self, *args, **kwargs):
    super().__init__(*args, **kwargs)
    self.max_steps = 3
    self.ee = RobotiqGripper  # Use gripper instead of suction
    self.lift_height = 0.15  # Target lift height (15cm)
    self.primitive = PickPlaceGripper(height=0.2, speed=0.005)
    self.cube_size = (0.08, 0.08, 0.08)  # 8cm cube (larger for better detection)
    self.pos_eps = 0.03  # Position tolerance for success
    
    # Store initial cube state
    self.cube_id = None
    self.initial_cube_pose = None

  def reset(self, env):
    super().reset(env)
    
    # Clear previous state
    self.cube_id = None
    self.initial_cube_pose = None
    
    # Add a single red cube at a random position
    cube_urdf = 'stacking/block.urdf'  # This is already red
    cube_pose = self.get_random_pose(env, self.cube_size)
    
    # Ensure the cube is placed flat on the table
    if cube_pose is not None:
      pos, orn = cube_pose
      # Keep only yaw rotation, set roll and pitch to 0
      _, _, yaw = utils.quatXYZW_to_eulerXYZ(orn)
      flat_orn = utils.eulerXYZ_to_quatXYZW((0, 0, yaw))
      cube_pose = (pos, flat_orn)
    else:
      # Fallback position if no random pose found
      cube_pos = (0.5, 0.0, self.cube_size[2] / 2)
      cube_orn = utils.eulerXYZ_to_quatXYZW((0, 0, 0))
      cube_pose = (cube_pos, cube_orn)
    
    # Add cube to environment
    self.cube_id = env.add_object(cube_urdf, cube_pose)
    self.initial_cube_pose = cube_pose
    
    # Define goal: lift the cube to target height
    target_pos = np.array(cube_pose[0]) + np.array([0, 0, self.lift_height])
    target_pose = (tuple(target_pos), cube_pose[1])
    
    # Add goal to task
    self.goals.append((
        [(self.cube_id, (0, None))],  # Objects to manipulate
        np.eye(1),  # Matching matrix
        [target_pose],  # Target poses
        False,  # Replace
        True,  # Rotations
        'pose',  # Metric
        None,  # Params
        1.0  # Max reward
    ))

  # Use the default oracle from Task base class
  # The base class oracle should work with grippers if we set up the goals correctly

  def reward(self):
    """Calculate reward based on cube height."""
    if self.cube_id is None or self.initial_cube_pose is None:
      return 0.0, {}
    
    # Get current cube position
    current_cube_pose = p.getBasePositionAndOrientation(self.cube_id)
    current_pos = current_cube_pose[0]
    
    # Calculate height increase
    initial_height = self.initial_cube_pose[0][2]
    current_height = current_pos[2]
    height_increase = current_height - initial_height
    
    # Check if cube is lifted to target height
    target_height_increase = self.lift_height
    success = height_increase >= (target_height_increase - 0.02)  # 2cm tolerance
    
    # Also check horizontal stability (cube shouldn't drift too far)
    initial_xy = np.array(self.initial_cube_pose[0][:2])
    current_xy = np.array(current_pos[:2])
    xy_distance = np.linalg.norm(current_xy - initial_xy)
    stable = xy_distance < self.pos_eps
    
    # Reward calculation
    step_reward = 1.0 if (success and stable) else 0.0
    
    # Calculate delta reward
    current_cumulative_reward = self.progress + step_reward
    delta_reward = current_cumulative_reward - self._rewards
    self._rewards = current_cumulative_reward
    
    # Update progress and goals
    info = {'lifted': success, 'stable': stable, 'height_increase': height_increase}
    if step_reward >= 0.99 and self.goals:
      self.progress += self.goals[0][7]  # Add max reward for this goal
      self.goals.pop(0)
      info['task_completed'] = True
    
    return delta_reward, info

  def done(self):
    """Check if task is completed."""
    return len(self.goals) == 0 or self._rewards > 0.99
